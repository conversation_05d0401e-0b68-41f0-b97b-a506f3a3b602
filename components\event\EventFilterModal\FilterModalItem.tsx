import React from "react";
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Gap from "src/core/components/Gap";
import globalStyles from "src/styles/global";

type ItemType = {
  name: string;
  value?: any;
};

const FilterModalItem = <T extends ItemType>({
  list,
  onPress,
  title,
  selected,
}: {
  title: string;
  selected?: T;
  list: T[];
  onPress: (item: T) => void;
}) => {
  const labelStyle = (item: T) =>
    item.name === selected?.name
      ? [styles.labelBase, styles.labelSelected]
      : styles.labelBase;

  const textStyle = (item: T) => {
    return item.name === selected?.name
      ? globalStyles.colors.white
      : globalStyles.colors.light.secondary;
  };

  return (
    <View
      style={{
        flexDirection: "column",
        justifyContent: "flex-start",
      }}
    >
      <Text
        style={{
          fontSize: globalStyles.fontSize.xl,
          color: globalStyles.colors.tertiary2,
        }}
      >
        {title}
      </Text>
      <Gap y={globalStyles.gap["2xs"]} />
      <ScrollView
        horizontal
        scrollIndicatorInsets={{
          top: 10,
        }}
        style={{
          margin: -5,
        }}
      >
        {list.map((item) => (
          <View
            key={item.value}
            style={{
              padding: 5,
            }}
          >
            <TouchableOpacity
              style={labelStyle(item)}
              onPress={() => onPress(item)}
            >
              <Text
                style={{
                  color: textStyle(item),
                }}
              >
                {item.name}
              </Text>
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  labelBase: {
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: 5,
    backgroundColor: globalStyles.colors.light.primary,
    borderRadius: globalStyles.rounded.full,
  },
  labelSelected: {
    backgroundColor: globalStyles.colors.primary1,
    color: globalStyles.colors.white,
  },
});

export default FilterModalItem;
