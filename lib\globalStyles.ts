const globalStyles = {
  rounded: {
    xs: 10 as const,
    sm: 20 as const,
    full: 1000 as const,
  },
  gap: {
    "2xs": 10 as const,
    xs: 20 as const,
    sm: 30 as const,
    md: 40 as const,
    lg: 80 as const,
    xl: 160 as const,
  },
  size: {
    pageTop: 40 as const,
    textAdjustLineHeight: 15 as const,
    xs: 8 as const,
    sm: 10 as const,
    md: 12 as const,
    lg: 14 as const,
    xl: 16 as const,
    "2xl": 18 as const,
    "3xl": 20 as const,
    "4xl": 24 as const,
    "5xl": 28 as const,
    "6xl": 32 as const,
    "7xl": 36 as const,
  },
  colors: {
    primary1: "#BE0068" as const,
    primary2: "#E5006B" as const,
    secondary1: "#F08300" as const,
    secondary2: "#E84621" as const,
    tertiary1: "#CABA9F" as const,
    tertiary2: "#6F6F6E" as const,
    tertiary3: "#c8b7bd" as const,
    light: {
      primary: "#E9E8ED" as const,
      secondary: "#838795" as const,
    },
    dark: {
      primary: "#2B2E3B" as const,
      secondary: "#4F5761" as const,
    },
    white: "#ffffff" as const,
  },
  rgba: ({ opacity = 1 }: { opacity?: number } = {}) => ({
    primary1: `rgba(190, 0, 104, ${opacity})` as const,
    primary2: `rgba(229, 0, 107, ${opacity})` as const,
    secondary1: `rgba(240, 131, 0, ${opacity})` as const,
    secondary2: `rgba(232, 70, 33, ${opacity})` as const,
    tertiary1: `rgba(203, 186, 159, ${opacity})` as const,
    tertiary2: `rgba(111, 111, 110, ${opacity})` as const,
    tertiary3: `rgba(200, 183, 189, ${opacity})` as const,
    light: {
      primary: `rgba(233, 232, 237, ${opacity})` as const,
      secondary: `rgba(131, 135, 149, ${opacity})` as const,
    },
    dark: {
      primary: `rgba(43, 46, 59, ${opacity})` as const,
      secondary: `rgba(79, 87, 97, ${opacity})` as const,
    },
    white: `rgba(255, 255, 255, ${opacity})` as const,
  }),
};

export default globalStyles;
