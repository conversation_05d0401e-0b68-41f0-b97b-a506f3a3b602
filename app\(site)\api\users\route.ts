import { deleteUserByIdController } from "@/app/(site)/user/controller";
import { NextRequest, NextResponse } from "next/server";
import { withApi<PERSON>eyAuth } from "@/lib/auth";

async function deleteHandler(req: NextRequest) {
  const requestData = await req.json();
  const { data, error } = await deleteUserByIdController(requestData);

  if (error) {
    return NextResponse.json({ error: error }, { status: 400 });
  }

  return NextResponse.json({ data });
}

export const DELETE = withApiKeyAuth(deleteHandler);
