generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Gender {
  MALE
  FEMALE
  PREFER_NOT_SAY
}

enum Role {
  USER
  EDITOR
  ADMIN
}

model User {
  id            String    @id @default(cuid())
  name          String    @db.VarChar(50)
  email         String    @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // from app
  phone     String?   @db.VarChar(9)
  website   String?   @db.VarChar(255)
  gender    Gender    @default(PREFER_NOT_SAY)
  birthDate DateTime?
  role      Role      @default(USER)

  eventsCheckout EventCheckout[]
  favoriteEvents FavoriteEvent[]

  isDeleted Boolean @default(false)
  isBlocked Boolean @default(false)

  Event    Event[]
  Category Category[]

  @@index([email, name])
  @@map("users")
}

model Account {
  id                    String    @id @default(cuid())
  type                  String
  accountId             String
  providerId            String
  accessToken           String?
  refreshToken          String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  idToken               String?
  password              String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("accounts")
}

model Session {
  id        String   @id @default(cuid())
  token     String
  expiresAt DateTime
  ipAddress String?
  userAgent String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("sessions")
}

model Verification {
  id         String    @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verifications")
}

model Media {
  id           String @id @default(uuid())
  filename     String @db.VarChar(200)
  originalName String @db.VarChar(200)
  mimetype     String @db.VarChar(200)
  url          String @unique @db.VarChar(200)

  events Event[]

  createdAt DateTime @default(now())

  @@map("medias")
}

model Event {
  id          String  @id @default(uuid())
  name        String  @db.VarChar(50)
  organizer   String  @db.VarChar(50)
  description String  @db.VarChar(500)
  location    String  @db.VarChar(100)
  published   Boolean @default(false)
  prices      Price[]

  eventCheckouts EventCheckout[]
  categories     Category[]
  medias         Media[]

  createdBy   User            @relation(fields: [createdById], references: [id], onDelete: Cascade)
  createdById String
  favorites   FavoriteEvent[]

  startAt          DateTime
  endAt            DateTime
  highlightedUntil DateTime?
  sponsoredUntil   DateTime?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  priceId          String?

  @@index([createdById])
  @@map("events")
}

model Price {
  id          String  @id @default(uuid())
  name        String  @db.VarChar(50)
  price       Decimal @db.Decimal(10, 2)
  description String? @db.VarChar(1000)

  event     Event?   @relation(fields: [eventId], references: [id], onDelete: Cascade)
  eventId   String?
  createdAt DateTime @default(now())

  @@unique([name, eventId])
  @@index([eventId])
  @@map("prices")
}

model FavoriteEvent {
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  eventId   String
  event     Event?   @relation(fields: [eventId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@id([userId, eventId])
  @@index([userId])
  @@index([eventId])
  @@map("favorite_events")
}

enum EventCheckoutType {
  WHATSAPP
  WEBSITE
  EMAIL
}

model EventCheckout {
  id        String            @id @default(uuid())
  eventId   String            @db.VarChar(50)
  event     Event             @relation(fields: [eventId], references: [id], onDelete: Cascade)
  users     User[]
  fields    Json?
  type      EventCheckoutType
  updatedAt DateTime          @updatedAt
  createdAt DateTime          @default(now())

  @@index([eventId])
  @@map("event_checkouts")
}

enum CategoryType {
  NORMAL
  CUSTOM
}

model Category {
  id          String @id @default(uuid())
  name        String @db.VarChar(30)
  description String @db.VarChar(500)

  type   CategoryType @default(NORMAL)
  events Event[]

  createdBy   User   @relation(fields: [createdById], references: [id], onDelete: Cascade)
  createdById String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([createdById])
  @@map("categories")
}
