import { client } from "@/sanity/lib/client";
import {
  getAllCategoriesQuery,
  getCategoryBySlugQuery,
} from "@/sanity/queries/category";
import { z } from "zod";
import { categorySchema } from "../schema";
import { LocaleType, DEFAULT_LANGUAGE } from "../../api/i18n";

export const getAllCategories = async (lang: LocaleType = DEFAULT_LANGUAGE) => {
  const categories = await client
    .fetch(getAllCategoriesQuery, { lang })
    .catch((err) => {
      console.error(err);
      return null;
    });

  const schema = z.array(categorySchema).safeParse(categories);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const getCategoryBySlug = async (
  slug: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const category = await client
    .fetch(getCategoryBySlugQuery, { slug, lang })
    .catch((err) => {
      console.error(err);
      return null;
    });

  const schema = categorySchema.safeParse(category);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};
