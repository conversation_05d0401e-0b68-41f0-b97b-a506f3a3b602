import type { StructureResolver } from "sanity/structure";

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title("Zimbora")
    .items([
      S.documentTypeListItem("event"),
      S.documentTypeListItem("category"),
      S.documentTypeListItem("notification"),
      S.divider(),
      ...S.documentTypeListItems().filter(
        (item) =>
          item.getId() &&
          !["event", "category", "author", "notification"].includes(
            item.getId()!,
          ),
      ),
    ]);
