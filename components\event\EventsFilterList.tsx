import React from "react";
import { View } from "react-native";
import Label from "src/categories/components/Label";
import { FilterItem } from "../stores/useEventFilterStore";

type Props = {
  filters: FilterItem[];
  onPress?: (item: FilterItem) => void;
};

const EventsFilterList = ({ filters, onPress }: Props) => {
  const handleOnPress = (text: string) => {
    const item = filters.find((v) => v.value === text);
    item && onPress?.(item);
  };

  return (
    <View
      style={{
        flexDirection: "row",
        flexWrap: "wrap",
        margin: -5,
      }}
    >
      {filters.map((item) => (
        <View
          key={item.value}
          style={{
            padding: 5,
          }}
        >
          <Label text={item.value} isSelected onPress={handleOnPress} />
        </View>
      ))}
    </View>
  );
};

export default EventsFilterList;
