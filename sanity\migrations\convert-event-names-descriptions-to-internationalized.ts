/**
 * Migration script to convert event names and descriptions from simple strings to internationalized objects
 *
 * This script converts:
 * name: "Event Name"
 * description: "Event Description"
 *
 * To:
 * name: [{ _key: 'pt', value: "Event Name" }]
 * description: [{ _key: 'pt', value: "Event Description" }]
 *
 * Usage:
 * - Set SANITY_API_TOKEN environment variable with write permissions
 * - Run: npx tsx sanity/migrations/convert-event-names-descriptions-to-internationalized.ts
 */

import { createClient } from "next-sanity";
import { apiVersion, dataset, projectId } from "../env";

// Create client with write token
const client = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false, // Don't use CDN for mutations
  token: process.env.SANITY_API_TOKEN,
});

type EventDocument = {
  _id: string;
  _type: "event";
  name: string | Array<{ _key: string; value: string }>;
  description: string | Array<{ _key: string; value: string }>;
  slug?: {
    _type: "slug";
    current: string;
  };
  organizer?: string;
  location?: string;
  startAt?: string;
  endAt?: string;
  highlightedUntil?: string;
  sponsoredUntil?: string;
  prices?: Array<any>;
  categories?: Array<any>;
  medias?: Array<any>;
  checkoutMethods?: Array<any>;
  createdBy?: string;
};

async function migrateEventNamesAndDescriptions() {
  console.log("🚀 Starting event names and descriptions migration...");

  try {
    // Check if we have a valid token
    if (!process.env.SANITY_API_TOKEN) {
      throw new Error("SANITY_API_TOKEN environment variable is required");
    }

    // Fetch all events
    console.log("📋 Fetching all events...");
    const events = await client.fetch<EventDocument[]>(
      `*[_type == "event"]{
        _id, 
        name, 
        description, 
        slug, 
        organizer, 
        location, 
        startAt, 
        endAt, 
        highlightedUntil, 
        sponsoredUntil, 
        prices, 
        categories, 
        medias, 
        checkoutMethods, 
        createdBy
      }`,
    );

    console.log(`📊 Found ${events.length} events to process`);

    if (events.length === 0) {
      console.log("✅ No events found. Migration complete.");
      return;
    }

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Process each event
    for (const event of events) {
      try {
        let needsUpdate = false;
        const updates: Record<string, any> = {};

        // Check and convert name field
        if (typeof event.name === "string") {
          updates.name = [
            {
              _key: "pt",
              value: event.name,
            },
          ];
          needsUpdate = true;
          console.log(`🔄 Converting name for event "${event._id}": "${event.name}"`);
        } else if (Array.isArray(event.name)) {
          console.log(`⏭️  Event "${event._id}" name already internationalized`);
        } else {
          console.log(`⚠️  Event "${event._id}" has invalid name type:`, typeof event.name);
        }

        // Check and convert description field
        if (typeof event.description === "string") {
          updates.description = [
            {
              _key: "pt",
              value: event.description,
            },
          ];
          needsUpdate = true;
          console.log(`🔄 Converting description for event "${event._id}"`);
        } else if (Array.isArray(event.description)) {
          console.log(`⏭️  Event "${event._id}" description already internationalized`);
        } else {
          console.log(`⚠️  Event "${event._id}" has invalid description type:`, typeof event.description);
        }

        // Update the document if needed
        if (needsUpdate) {
          await client
            .patch(event._id)
            .set(updates)
            .commit();

          migratedCount++;
          console.log(`✅ Successfully migrated event "${event._id}"`);
        } else {
          skippedCount++;
          console.log(`⏭️  Skipping event "${event._id}" - already internationalized`);
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Error migrating event "${event._id}":`, error);
      }
    }

    // Summary
    console.log("\n📈 Migration Summary:");
    console.log(`✅ Successfully migrated: ${migratedCount} events`);
    console.log(`⏭️  Skipped (already migrated): ${skippedCount} events`);
    console.log(`❌ Errors: ${errorCount} events`);
    console.log(`📊 Total processed: ${events.length} events`);

    if (errorCount > 0) {
      console.log(
        "\n⚠️  Some events failed to migrate. Please check the errors above.",
      );
      process.exit(1);
    } else {
      console.log("\n🎉 Migration completed successfully!");
    }
  } catch (error) {
    console.error("💥 Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateEventNamesAndDescriptions()
    .then(() => {
      console.log("🏁 Migration script finished");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Migration script failed:", error);
      process.exit(1);
    });
}

export { migrateEventNamesAndDescriptions };
