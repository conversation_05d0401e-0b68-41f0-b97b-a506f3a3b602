"use client";
import Button from "@/features/client/core/components/Button";
import {
  SignedIn,
  SignedOut,
  SignInButton,
  SignUpButton,
  useUser,
} from "@clerk/nextjs";
import { useActionState } from "react";
import { useFormStatus } from "react-dom";
import { deleteAccountServerAction } from "../user/actions";

const SubmitButton = () => {
  const { pending } = useFormStatus();
  return (
    <Button
      className="w-fit"
      disabled={pending}
      isLoading={pending}
      type="submit"
    >
      Excluir Conta
    </Button>
  );
};

type Result = Awaited<ReturnType<typeof deleteAccountServerAction>>;

const DeleteAccountPage = () => {
  const [result, deleteAccount] = useActionState<Result, FormData>(
    (_, formData) => deleteAccountServerAction(formData),
    { data: false, error: "" },
  );

  const { user } = useUser();

  return (
    <main className="mx-auto grid max-w-4xl px-4 py-8">
      <h1 className="mb-4 text-2xl font-bold">Excluir Conta</h1>
      <SignedOut>
        <p className="mb-6">
          Para excluir sua conta, é necessário fazer login primeiro. Por favor,
          inicie a sessão para prosseguir com a exclusão da conta.
        </p>
        <SignInButton>
          <Button className="w-fit">Iniciar sessão</Button>
        </SignInButton>
        {/* <SignUpButton /> */}
      </SignedOut>
      <SignedIn>
        <p className="mb-6">
          Ficamos triste em saber que você está pensando em excluir sua conta{" "}
          <b>{user?.firstName}</b>, antes de excluir sua conta, gostaríamos de
          entender o motivo. Por favor, compartilhe conosco o motivo da
          exclusão:
        </p>
        <form action={deleteAccount} className="flex flex-col gap-4">
          <textarea
            name="reason"
            className="w-full rounded-md border border-gray-300 p-2"
            rows={4}
            minLength={2}
            maxLength={300}
            placeholder="Digite o motivo da exclusão (entre 2 e 300 caracteres)"
            required
          />
          {result.error && <p className="text-red-500">{result.error}</p>}
          <SubmitButton />
        </form>
      </SignedIn>{" "}
      {result.data && (
        <p className="mt-4 text-green-500">Conta excluída com sucesso!</p>
      )}
    </main>
  );
};

export default DeleteAccountPage;
