import {
  PhoneIcon,
  PlusCircleIcon,
  MagnifyingGlassIcon,
  BookmarkIcon,
} from "@heroicons/react/24/outline";

type FeatureInfo = {
  title: string;
  description: string;
  Icon: React.ComponentType<React.ComponentProps<"svg">>;
};

const findEventsInfo = {
  title: "Encontre os melhores eventos",
  description:
    "Com o nosso aplicativo, você pode encontrar eventos do seu interesse e que estão próximos de você.",
  Icon: MagnifyingGlassIcon,
};

const directContactInfo = {
  title: "Entre em contato através do Zimbora",
  description:
    "Com o Zimbora, não é necessário se preocupar com o contato direto com o evento. Basta acessar o aplicativo e se cadastrar.",
  Icon: PhoneIcon,
};

const saveEventsInfo = {
  title: "Histórico de eventos",
  description:
    "Com o Zimbora, você pode salvar eventos que você já participou ou está interessado, e poderá receber descontos em novos eventos.",
  Icon: BookmarkIcon,
};

const muchMoreInfo = {
  title: "Temos Muito mais vindo!",
  description:
    "O Zimbora está em constante evolução, isso significa que temos muito mais funcionalidades para você. Entre em contato com a gente e veja o que temos para oferecer.",
  Icon: PlusCircleIcon,
};

export const featuresInfo: FeatureInfo[] = [
  findEventsInfo,
  directContactInfo,
  saveEventsInfo,
  muchMoreInfo,
];
