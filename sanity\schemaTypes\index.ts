import { type SchemaTypeDefinition } from "sanity";

import { blockContentType } from "./blockContentType";
import { categoryType } from "./categoryType";
import { eventType } from "./eventType";
// import { postType } from "./postType";
// import { authorType } from "./authorType";
import { favoriteEventType } from "./favoriteEventType";
import { notificationType } from "./notificationType";

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    blockContentType,
    categoryType,
    eventType,
    favoriteEventType,
    notificationType,
  ],
};
