{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Next.js launch debug client",
            "request": "launch",
            "type": "msedge",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}",
            "sourceMapPathOverrides": {
                "/turbopack/[project]/*": "${webRoot}/*"
            }
        },
        {
            "name": "Next.js attach debug server",
            "port": 9230,
            "request": "attach",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node",
            "sourceMapPathOverrides": {
                "/turbopack/[project]/*": "${webRoot}/*"
            }
        },
    ],
    "compounds": [
        {
            "name": "Next.js launch debug",
            "configurations": [
                "Next.js launch debug client",
                "Next.js attach debug server"
            ],
            "stopAll": true
        }
    ]
}