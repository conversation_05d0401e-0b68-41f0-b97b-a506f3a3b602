import type { Metada<PERSON> } from "next";
import "@/styles/globals.css";
import ClientWrapper from "./ClientWrapper";
import { Inter } from "next/font/google";
import Header from "@/features/client/website/components/Header";
import { ClerkProvider } from "@clerk/nextjs";
import Footer from "@/features/client/website/components/Footer";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Zimbor<PERSON>",
  description: "A melhor plataforma de eventos de Luanda",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR" className={`${inter.variable} font-sans`}>
      <body>
        <ClerkProvider>
          <Header />
          <ClientWrapper>{children}</ClientWrapper>
          <Footer />
          <Toaster />
        </ClerkProvider>
      </body>
    </html>
  );
}
