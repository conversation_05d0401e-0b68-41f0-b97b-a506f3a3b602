import { defineField, defineType } from "sanity";

export const favoriteEventType = defineType({
  name: "favoriteEvent",
  title: "Eventos favoritos",
  type: "document",
  // readOnly: true,
  fields: [
    defineField({
      name: "userId",
      title: "ID do Usuário",
      type: "string",
      validation: (rule) =>
        rule.required().error("ID do Usuário é obrigatório"),
    }),
    defineField({
      name: "eventId",
      title: "ID do Evento",
      type: "string",
      validation: (rule) => rule.required().error("ID do Evento é obrigatório"),
    }),
    defineField({
      name: "event",
      title: "Evento",
      type: "reference",
      to: [{ type: "event" }],
      validation: (rule) =>
        rule.required().error("Referência do Evento é obrigatória"),
    }),
  ],
  preview: {
    select: {
      userId: "userId",
      eventTitle: "event.name",
    },
    prepare({ userId, eventTitle }) {
      return {
        title: `${userId} ♥ ${eventTitle || "Evento desconhecido"}`,
      };
    },
  },
});
