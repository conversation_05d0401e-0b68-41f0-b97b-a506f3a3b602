const ngrok = require("@ngrok/ngrok");

// setup ngrok ingress in the parent process,
// in forked processes "send" will exist.
const makeListener = process.send === undefined;
let host = "localhost";
let port = process.env.PORT || "3000";
const url = "lenient-turkey-moderately.ngrok-free.app";

process.argv.forEach((item, index) => {
  if (["--hostname", "-H"].includes(item)) host = process.argv[index + 1];
  if (["--port", "-p"].includes(item)) port = process.argv[index + 1];
});

async function setup() {
  const session = await new ngrok.SessionBuilder().authtokenFromEnv().connect();
  const listener = await session.httpEndpoint().domain(url).listen();
  console.log(
    `Forwarding to: ${host}:${port} from ingress at: ${listener.url()}`,
  );
  listener.forward(`${host}:${port}`);
  return listener.url();
}

if (makeListener) setup();
