import { NextRequest, NextResponse } from "next/server";

type ParamsType = { [key: string]: string };

export type ApiHandlerProps<PT = ParamsType> = (
  req: NextRequest,
  context: { params: Promise<PT> },
) => Promise<NextResponse>;

export const withApiKeyAuth = <PT = ParamsType>(
  handler: ApiHandlerProps<PT>,
) => {
  return async (req: NextRequest, context: { params: Promise<PT> }) => {
    const apiKey = req.headers.get("X-API-Key");
    const validApiKey = process.env.INTERNAL_API_KEY;

    if (!validApiKey) {
      console.log("Erro: Chave de API interna não definida");
      return NextResponse.json({ error: "Erro do Servidor" }, { status: 500 });
    }

    if (!apiKey || apiKey !== validApiKey) {
      console.log("Chave de API inválida ou ausente");
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    return handler(req, context);
  };
};
