import { View, Text, StyleSheet, Image, ImageBackground } from "react-native";
import React from "react";
import globalStyles from "../../../styles/global";
import Gap from "../../../core/components/Gap";
import Button from "../../../core/components/Button/Button";
import { MyCategoryType } from "src/categories/models/MyCategory";
import { useScreens } from "src/screens/hooks/useScreens";

type Props = {
  category: MyCategoryType;
};

const EventCategoryCard = ({ category }: Props) => {
  const { navigateTo } = useScreens();
  return (
    <ImageBackground
      resizeMode="cover"
      style={styles.container}
      source={require("assets/images/meeting_room.jpg")}
    >
      <View style={styles.backdrop} />
      <Text style={styles.title}>{category.name}</Text>
      <Gap y={globalStyles.gap["2xs"]} />
      <Text style={styles.subText}>{category.description}</Text>
      <Gap y={globalStyles.gap.xs} />
      <Button
        text="Ver Mais"
        onPress={() =>
          navigateTo("Pesquisa", {
            category,
          })
        }
      />
    </ImageBackground>
    // </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    padding: globalStyles.gap.xs,
    borderRadius: globalStyles.rounded.sm,
    backgroundColor: globalStyles.colors.light.primary,
    maxWidth: 300,
    overflow: "hidden",
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: globalStyles.colors.dark.primary,
    opacity: 0.5,
  },
  title: {
    fontSize: globalStyles.fontSize["2xl"],
    color: globalStyles.colors.white,
    fontWeight: "bold",
  },
  subText: {
    fontSize: globalStyles.fontSize["md"],
    color: globalStyles.colors.light.primary,
    fontWeight: "500",
  },
});

export default EventCategoryCard;
