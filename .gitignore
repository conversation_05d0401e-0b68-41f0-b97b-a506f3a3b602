# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**node_modules**
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

pnpm-lock.yaml

# local env files
.env*
.env*.local

rest-client.env.json

# vercel
.vercel

# typescript
*.tsbuildinfo

.vercel


dev.tar.gz
