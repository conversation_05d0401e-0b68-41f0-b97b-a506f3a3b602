import type { NextConfig } from "next";
if (process.env.NODE_ENV === "development") {
  import("./ngrok.config.js");
}

const nextConfig: NextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      { hostname: "res.cloudinary.com" },
      { hostname: "loremflickr.com" },
      { hostname: "images.unsplash.com" },
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
      },
    ],
  },
  async headers() {
    return [
      {
        source: "/.well-known/apple-app-site-association",
        headers: [{ key: "content-type", value: "application/json" }],
      },
    ];
  },
};

export default nextConfig;
