import { ArrowPathIcon } from "@heroicons/react/24/outline";
import React from "react";
import { cn } from "@/lib/utils";

type Props = React.DetailedHTMLProps<
  React.ButtonHTMLAttributes<HTMLButtonElement>,
  HTMLButtonElement
> & {
  theme?: "primary" | "secondary" | "danger" | "tertiary" | "link";
  size?: "small" | "medium";
  isLoading?: boolean;
  ref?: React.Ref<HTMLButtonElement>;
};

export const buttonStyles = {
  variants: {
    primary:
      "rounded-full bg-primary1 py-2 px-4 text-white duration-200 hover:bg-primary2 active:opacity-60 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-primary1",
    secondary:
      "rounded-full bg-light-primary py-2 px-4 text-light-secondary duration-200 hover:bg-light-secondary hover:text-light-primary active:opacity-60 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-light-primary",
    tertiary:
      "rounded-full bg-tertiary1 py-2 px-4 text-white duration-200 hover:bg-light-secondary hover:text-light-primary active:opacity-60 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-tertiary1",
    danger:
      "rounded-full bg-light-secondary py-2 px-4 text-white duration-200 hover:bg-secondary2 hover:text-white active:opacity-60 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-light-secondary",
    link: "rounded-full bg-transparent py-2 px-4 text-tertiary2 duration-200 hover:bg-transparent hover:text-primary2 active:opacity-60 disabled:cursor-not-allowed disabled:opacity-70 disabled:hover:bg-transparent",
  },
  sizes: {
    small: "px-3 py-1 text-sm",
    medium: "px-4 py-2 text-base",
  },
};

const Button = ({
  children,
  className = "",
  theme = "primary",
  size = "medium",
  isLoading = false,
  ref,
  ...props
}: Props) => {
  const themeStyles = buttonStyles.variants[theme];
  const sizeStyles = buttonStyles.sizes[size];

  return (
    <button
      ref={ref}
      {...props}
      className={cn(themeStyles, sizeStyles, className)}
    >
      {isLoading ? (
        <ArrowPathIcon className="mx-auto h-5 w-5 animate-spin stroke-white" />
      ) : (
        children
      )}
    </button>
  );
};

export default Button;
