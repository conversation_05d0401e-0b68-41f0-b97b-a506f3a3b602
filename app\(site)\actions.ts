"use server";

import { z } from "zod";
import { sendEmail, createBetaSignupEmail } from "@/lib/mailer";
import { betaSignupSchema } from "./schema";

type ActionResult = {
  success: boolean;
  message: string;
};

export async function submitBetaSignupAction(
  email: string,
): Promise<ActionResult> {
  try {
    const validatedData = betaSignupSchema.parse({ email });

    const emailData = createBetaSignupEmail(validatedData.email);
    await sendEmail(emailData);

    return {
      success: true,
      message:
        "Solicitação enviada! Você receberá um email com o link de download da Play Store em breve.",
    };
  } catch (error) {
    console.error("Beta signup error:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: error.errors[0]?.message || "Dados inválidos",
      };
    }

    return {
      success: false,
      message: "Erro ao enviar solicitação. Tente novamente.",
    };
  }
}
