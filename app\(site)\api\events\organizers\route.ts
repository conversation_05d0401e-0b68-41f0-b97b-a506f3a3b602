import { NextRequest, NextResponse } from "next/server";
import { getEventsOrganizers } from "@/app/(site)/event/controller";
import { withApi<PERSON>eyAuth } from "@/lib/auth";

async function getHandler(request: NextRequest) {
  try {
    const organizers = await getEventsOrganizers();

    if (!organizers) {
      return NextResponse.json(
        { error: "Falha ao buscar organizadores de eventos" },
        { status: 400 },
      );
    }

    return NextResponse.json({ data: organizers });
  } catch (error) {
    console.error("Event Organizers API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApiKeyAuth(getHandler);
