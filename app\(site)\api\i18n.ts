export const LOCALES = ["pt", "en"] as const;
export type LocaleType = (typeof LOCALES)[number];

export const DEFAULT_LANGUAGE: LocaleType = "pt";
/**
 * Parse Accept-Language header and return the best matching supported language
 * @param acceptLanguageHeader - The Accept-Language header value
 * @returns The best matching supported language or default language
 */
export function parseAcceptLanguage(
  acceptLanguageHeader?: string | null,
): LocaleType {
  if (!acceptLanguageHeader) {
    return DEFAULT_LANGUAGE;
  }

  try {
    // Parse the Accept-Language header
    // Format: "en-US,en;q=0.9,pt;q=0.8,pt-BR;q=0.7"
    const languages = acceptLanguageHeader
      .split(",")
      .map((lang) => {
        const [code, qValue] = lang.trim().split(";");
        const quality = qValue ? parseFloat(qValue.split("=")[1]) : 1.0;

        // Extract the primary language code (e.g., "en" from "en-US")
        const primaryCode = code.split("-")[0].toLowerCase();

        return {
          code: primaryCode,
          quality: isNaN(quality) ? 1.0 : quality,
        };
      })
      .sort((a, b) => b.quality - a.quality); // Sort by quality (highest first)

    // Find the first supported language
    for (const lang of languages) {
      if (LOCALES.includes(lang.code as LocaleType)) {
        return lang.code as LocaleType;
      }
    }
  } catch (error) {
    console.warn("Error parsing Accept-Language header:", error);
  }

  return DEFAULT_LANGUAGE;
}
/**
 * Extract language from Next.js request headers
 * @param headers - Request headers object
 * @returns The detected language or default language
 */
export function getLanguageFromHeaders(
  headers: Headers | Record<string, string | string[] | undefined>,
): LocaleType {
  let acceptLanguage: string | null = null;

  if (headers instanceof Headers) {
    acceptLanguage = headers.get("accept-language");
  } else {
    const headerValue = headers["accept-language"];
    acceptLanguage = Array.isArray(headerValue)
      ? headerValue[0]
      : headerValue || null;
  }

  return parseAcceptLanguage(acceptLanguage);
}
/**
 * Validate if a language code is supported
 * @param lang - Language code to validate
 * @returns True if the language is supported
 */
export function isSupportedLanguage(lang: string): lang is LocaleType {
  return LOCALES.includes(lang as LocaleType);
}

/**
 * Get language from query parameters or headers with fallback
 * @param props - Object containing query language and headers
 * @returns The resolved language
 */
export function resolveLanguage({
  queryLang,
  headers,
}: {
  queryLang?: string | null;
  headers?: Headers | Record<string, string | string[] | undefined>;
}): LocaleType {
  // First, try query parameter
  if (queryLang && isSupportedLanguage(queryLang)) {
    return queryLang;
  }

  // Then, try headers
  if (headers) {
    return getLanguageFromHeaders(headers);
  }

  // Finally, fallback to default
  return DEFAULT_LANGUAGE;
}
