import { clerkClient } from "@clerk/nextjs/server";
import { z } from "zod";
import { tryCatch } from "@/lib/error";

export const deleteUserSchema = z.object({
  userId: z.string(),
});

export const deleteUserByIdController = async (data: { userId: string }) => {
  const validation = deleteUserSchema.safeParse(data);

  if (!validation.success) {
    return { data: null, error: "Usuário não identificado" };
  }

  const { userId } = validation.data;

  const { data: deleteData, error } = await tryCatch(
    (await clerkClient()).users.deleteUser(userId),
  );

  if (error) {
    return { data: null, error: "Falha ao excluir o usuário" };
  }

  return { data: deleteData.id, error: null };
};
