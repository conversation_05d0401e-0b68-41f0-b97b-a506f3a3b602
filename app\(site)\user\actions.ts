"use server";

import { deleteUserByIdController } from "./controller";
import { auth } from "@clerk/nextjs/server";
import { z } from "zod";

const deleteAccountSchema = z.object({
  reason: z
    .string({ invalid_type_error: "A mensagem deve ser um texto" })
    .min(2, "A mensagem deve ter no mínimo 2 caracteres")
    .max(300, "A mensagem deve ter no máximo 300 caracteres"),
});

export const deleteAccountServerAction = async (formData: FormData) => {
  try {
    const validatedData = deleteAccountSchema.safeParse({
      reason: formData.get("reason"),
    });

    if (!validatedData.success) {
      return {
        data: false,
        error: validatedData.error.errors[0]?.message || "Valor inválido",
      };
    }

    const { userId } = await auth();

    if (!userId) {
      return { data: false, error: "Usuário não autenticado" };
    }

    // Here you could log the reason before deleting
    console.log(`User ${userId} deletion reason: ${validatedData.data.reason}`);

    const result = await deleteUserByIdController({ userId });

    return result;
  } catch (error) {
    console.error("Delete account error:", error);
    return { data: false, error: "Ocorreu um erro inesperado" };
  }
};
