import { Expo } from "expo-server-sdk";
import { getAllEvents } from "@/app/(site)/event/controller";
import { EventType } from "@/app/(site)/event/schema";
import {
  createEventNotificationController,
  createNotificationController,
} from "@/app/(site)/event/controller/notifications";
import { NextRequest } from "next/server";

export async function GET() {
  try {
    // Use default language for notifications
    const latestEvents = await getAllEvents(
      {
        sortBy: "_createdAt",
        sortOrder: "desc",
        limit: 1,
      },
      "pt",
    );

    if (!latestEvents || latestEvents.length === 0) {
      return Response.json({ message: "No events found" }, { status: 404 });
    }

    const latestEvent: EventType = latestEvents[0];

    const res = await createEventNotificationController({
      event: latestEvent,
    });

    return Response.json(res, { status: res.status });
  } catch (error) {
    console.error("Error in notification route:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const body = await request.json();

  const res = await createNotificationController({ data: body });

  return Response.json(res, { status: res.status });
}
