import Image from "next/image";
import React from "react";
import { featuresInfo } from "../info";

// import { Container } from './styles';

const title = "Conheça o que temos para oferecer";

const FeatureSection: React.FC = () => {
  return (
    <section
      id="product"
      className="grid scroll-m-16 justify-items-center gap-8 md:grid-cols-2"
    >
      <h2 className="col-span-full text-3xl font-semibold text-dark-primary">
        {title}
      </h2>
      <article className="col-span-full grid content-start gap-4 sm:grid-cols-2">
        <Image
          alt="bubble phone"
          key="image"
          width={1080}
          height={1080}
          src="/images/bubble_phone.png"
          className="order-3 col-span-full mx-auto w-4/5 object-contain  sm:w-1/2 lg:order-1 lg:col-start-2 lg:row-span-4 lg:row-start-1 lg:w-full"
        />
        {featuresInfo.map((feature, i) => (
          <div
            key={i}
            className="grid grid-cols-4 content-start items-start gap-x-4 gap-y-1 lg:col-end-2"
            style={{
              order: i + 1,
            }}
          >
            <span className="col-end-1 row-span-2 block rounded-lg bg-tertiary1/30 p-2 text-primary1">
              <feature.Icon className="h-4 w-4 flex-shrink-0" />
            </span>
            <h6 className="col-span-full col-start-1 row-end-1 text-lg font-semibold text-dark-primary">
              {feature.title}
            </h6>
            <p className="col-span-full col-start-1 text-dark-secondary">
              {feature.description}
            </p>
          </div>
        ))}
      </article>
    </section>
  );
};

export default FeatureSection;
