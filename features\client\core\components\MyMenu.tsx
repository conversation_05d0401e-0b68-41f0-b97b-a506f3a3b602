import { Menu } from "@headlessui/react";
import Link from "next/link";

type Props = {
  className?: string;
  buttonClassName?: string;
  children: React.ReactNode;
  listActions: {
    href?: string;
    text: string;
    onClick?: () => void;
  }[];
};

const MyMenu = ({
  children,
  className = "",
  buttonClassName = "",
  listActions,
}: Props) => {
  return (
    <Menu as="div" className={`relative ${className}`}>
      <Menu.Button as="button" className={buttonClassName}>
        {children}
      </Menu.Button>
      <Menu.Items
        as="ul"
        className="absolute right-0  origin-top-right rounded-md border-t  border-light-primary bg-slate-100 p-4 text-base shadow-lg"
      >
        {listActions.map((action) => (
          <Menu.Item
            key={action.text}
            as="li"
            onClick={() => action.onClick?.()}
          >
            {!action.href ? (
              <button
                className={`w-full whitespace-nowrap hover:text-primary2 focus:text-primary2`}
              >
                {action.text}
              </button>
            ) : (
              <Link
                href={action.href}
                className={`w-full whitespace-nowrap hover:text-primary2 focus:text-primary2`}
              >
                {action.text}
              </Link>
            )}
          </Menu.Item>
        ))}
      </Menu.Items>
    </Menu>
  );
};
export default MyMenu;
