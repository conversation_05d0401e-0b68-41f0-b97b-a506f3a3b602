import { NextRequest, NextResponse } from "next/server";
import { withA<PERSON><PERSON>eyAuth } from "@/lib/auth";
import { resolveLanguage } from "../../i18n";
import { getAllCategories } from "@/app/(site)/category/controller/get";

async function getHandler(request: NextRequest) {
  try {
    const lang = resolveLanguage({ headers: request.headers });

    const categories = await getAllCategories(lang);

    if (!categories) {
      return NextResponse.json(
        { error: "Falha ao buscar categorias de eventos" },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: categories });
  } catch (error) {
    console.error("Event Categories API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApiKeyAuth(getHandler);
