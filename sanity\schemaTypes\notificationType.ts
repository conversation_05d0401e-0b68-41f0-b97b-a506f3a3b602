import { MessageSquare } from "lucide-react";
import { defineField, defineType } from "sanity";

export const notificationType = defineType({
  name: "notification",
  title: "Notificações",
  type: "document",
  icon: MessageSquare,
  fields: [
    // defineField({
    //   name: "title",
    //   title: "<PERSON>ítu<PERSON>",
    //   type: "string",
    //   validation: (rule) =>
    //     rule.max(50).warning("O título deve ter no máximo 50 caracteres"),
    // }),
    defineField({
      name: "description",
      title: "Descrição",
      type: "text",
      validation: (rule) =>
        rule.max(150).warning("A descrição deve ter no máximo 150 caracteres"),
    }),
    defineField({
      name: "event",
      title: "Evento",
      type: "reference",
      to: [{ type: "event" }],
      validation: (rule) => rule.required().error("O evento é obrigatório"),
    }),
  ],
  preview: {
    select: {
      title: "event.name",
      subtitle: "event.description",
    },
  },
});
