import { client, clientWithToken } from "@/sanity/lib/client";
import { z } from "zod";
import { eventSchema } from "../schema";
import {
  getUserFavoriteEventsQuery,
  hasUserFavoritedQuery,
} from "@/sanity/queries/event";
import { LocaleType, DEFAULT_LANGUAGE } from "../../api/i18n";

const createFavoriteEventId = ({
  userId,
  eventId,
}: {
  userId: string;
  eventId: string;
}) => {
  return `${userId}_-_${eventId}`;
};

export const toggleFavoriteEvent = async ({
  userId,
  eventId,
}: {
  userId: string;
  eventId: string;
}): Promise<void> => {
  const hasFavorited = await hasUserFavorited({ eventId, userId });

  if (hasFavorited) {
    await clientWithToken.delete(hasFavorited._id).catch((err) => {
      console.error("Failed to remove favorite:", err);
      throw err;
    });
  } else {
    await clientWithToken
      .createIfNotExists({
        _id: createFavoriteEventId({ userId, eventId }),
        _type: "favoriteEvent",
        userId,
        eventId,
        event: {
          _type: "reference",
          _ref: eventId,
        },
      })
      .catch((err) => {
        console.error("Failed to add favorite:", err);
        throw err;
      });
  }
};

export const getUserFavoriteEvents = async (
  userId: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const events = await client
    .fetch<
      { event: any }[]
    >(getUserFavoriteEventsQuery, { userId, lang }, { cacheMode: "noStale" })
    .catch((err) => {
      console.error(err);
      return null;
    });

  if (!events) {
    return null;
  }

  const schema = z.array(eventSchema).safeParse(events);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const hasUserFavorited = async ({
  eventId,
  userId,
}: {
  eventId: string;
  userId: string;
}) => {
  const result = await client
    .fetch<{
      _id: string;
    }>(hasUserFavoritedQuery, { eventId, userId }, { cacheMode: "noStale" })
    .catch((err) => {
      console.error(err);
      return null;
    });

  return result;
};
