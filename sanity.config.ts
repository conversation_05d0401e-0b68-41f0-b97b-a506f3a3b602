"use client";

import { visionTool } from "@sanity/vision";
import { defineConfig } from "sanity";
import { structureTool } from "sanity/structure";
// import { documentInternationalization } from "@sanity/document-internationalization";
import { internationalizedArray } from "sanity-plugin-internationalized-array";

import { apiVersion, dataset, projectId } from "./sanity/env";
import { schema } from "./sanity/schemaTypes";
import { structure } from "./sanity/structure";

export default defineConfig({
  basePath: "/studio",
  projectId,
  dataset,
  // Add and edit the content schema in the './sanity/schemaTypes' folder
  schema,
  plugins: [
    structureTool({ structure }),
    // Vision is for querying with GROQ from inside the Studio
    // https://www.sanity.io/docs/the-vision-plugin
    visionTool({ defaultApiVersion: apiVersion }),
    // documentInternationalization({
    //   supportedLanguages: [
    //     { id: "pt", title: "Português" },
    //     { id: "en", title: "English" },
    //   ],
    //   schemaTypes: ["event"],
    // }),
    internationalizedArray({
      languages: [
        { id: "pt", title: "Português" },
        { id: "en", title: "English" },
      ],
      defaultLanguages: ["pt"],
      fieldTypes: ["string", "text"],
      buttonLocations: ["field"],
      buttonAddAll: false,
      languageDisplay: "codeOnly", // codeOnly (default) | titleOnly | titleAndCode
    }),
  ],
});
