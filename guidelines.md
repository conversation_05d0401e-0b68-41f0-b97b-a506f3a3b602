# Guidelines for Zimbora Web

## Project Overview

**Zimbora Web** is a Next.js 15 application for an event platform in Luanda, Angola. The project uses modern web technologies and follows React Server Components patterns.

### Tech Stack

- **Framework**: Next.js 15.3.3 with React 19.1.0
- **Language**: TypeScript 5.8.3
- **Styling**: Tailwind CSS 4.1.8 + shadcn/ui (New York style)
- **Database**: Prisma with PostgreSQL
- **CMS**: Sanity
- **Authentication**: Clerk
- **State Management**: Zustand
- **Package Manager**: pnpm
- **Testing**: Vitest
- **Deployment**: Vercel (inferred)

## Code Organization

### Directory Structure

```
├── app/                    # Next.js App Router
│   ├── (site)/            # Main website routes
│   └── (studio)/          # Sanity Studio routes
├── components/            # Shared UI components
│   └── ui/               # shadcn/ui components
├── features/             # Feature-based organization
│   └── client/
│       ├── core/         # Core client features
│       └── website/      # Website-specific features
├── lib/                  # Utility libraries and configurations
├── prisma/              # Database schema and migrations
├── sanity/              # Sanity CMS configuration
└── styles/              # Global styles
```

### File Naming Conventions

- **Components**: PascalCase (e.g., `AppDownloadButtons.tsx`)
- **Pages**: lowercase with hyphens (e.g., `privacy-policy/`)
- **Utilities**: camelCase (e.g., `app-config.ts`)
- **Types**: PascalCase with `.types.ts` suffix
- **Constants**: UPPER_SNAKE_CASE in config files

## Component Guidelines

### React Component Patterns

1. **Use TypeScript types instead of interfaces** (user preference)

```typescript
// ✅ Preferred
type AppDownloadButtonsProps = {
  className?: string;
  variant?: "badges" | "text";
  showApk?: boolean;
  size?: "sm" | "md" | "lg";
};

// ❌ Avoid
interface AppDownloadButtonsProps {
  // ...
}
```

2. **Always wrap className with cn() function**

```typescript
import { cn } from "@/lib/utils";

const Component = ({ className }: { className?: string }) => {
  return (
    <div className={cn("default-classes", className)}>
      Content
    </div>
  );
};
```

3. **Use React Server Components by default**

- Components are server components unless they need client-side interactivity
- Add `"use client"` directive only when necessary

4. **Component Structure**

```typescript
// 1. Imports (external first, then internal)
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/lib/utils";

// 2. Types
type ComponentProps = {
  // props definition
};

// 3. Component
const Component = ({ prop1, prop2 }: ComponentProps) => {
  // 4. Logic and state

  // 5. Early returns
  if (condition) return null;

  // 6. Render
  return (
    <div>
      {/* JSX */}
    </div>
  );
};

// 7. Export
export default Component;
```

### Link Behavior

- **External links should open in new tabs** (user preference)

```typescript
<Link
  href={externalUrl}
  target="_blank"
  rel="noopener noreferrer"
>
  External Link
</Link>
```

## Styling Guidelines

### Tailwind CSS + shadcn/ui

1. **shadcn/ui Configuration**

   - Style: "new-york"
   - Base color: "zinc"
   - CSS variables enabled
   - RSC (React Server Components) enabled

2. **Class Organization**

```typescript
// Group related classes logically
className={cn(
  // Layout
  "flex flex-col items-center",
  // Spacing
  "space-y-4 p-4",
  // Styling
  "rounded-lg bg-gradient-to-r shadow",
  // Responsive
  "sm:flex-row sm:p-6",
  // Custom
  className
)}
```

3. **Responsive Design**

   - Mobile-first approach
   - Use Tailwind breakpoints: `sm:`, `md:`, `lg:`, `xl:`

4. **Color System**
   - Use CSS custom properties for theme colors
   - Primary colors: `primary1`, `primary2`
   - Text colors: `text-light-primary`

## TypeScript Guidelines

### Type Definitions

1. **Component Props**

```typescript
type ComponentProps = {
  required: string;
  optional?: number;
  variant?: "option1" | "option2";
  children?: React.ReactNode;
  className?: string;
};
```

2. **Configuration Objects**

```typescript
export const APP_CONFIG = {
  // configuration
} as const;

// Extract types from config
type AppConfig = typeof APP_CONFIG;
```

3. **API Responses**

```typescript
type ApiResponse<T> = {
  data: T;
  success: boolean;
  message?: string;
};
```

### Path Aliases

- Use `@/` for root-level imports
- Configured in `tsconfig.json` and `components.json`

## API and Data Guidelines

### Prisma

- Database schema in `prisma/schema.prisma`
- Migrations in `prisma/migrations/`
- Seed data in `prisma/seed.ts`

### Sanity CMS

- Configuration in `sanity/`
- Queries in `sanity/queries/`
- Schema types in `sanity/schemaTypes/`

### API Routes

- Use Next.js App Router API routes
- Located in `app/(site)/api/`

## Best Practices

1. **Performance**
   - Use Next.js Image component for images
   - Implement proper loading states
   - Use React Server Components when possible

This guideline serves as the foundation for maintaining code quality and consistency across the Zimbora Web project.
