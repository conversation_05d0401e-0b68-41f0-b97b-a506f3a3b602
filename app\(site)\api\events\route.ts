import { NextRequest, NextResponse } from "next/server";
import { getAllEvents } from "@/app/(site)/event/controller";
import { withApi<PERSON><PERSON>Auth } from "@/lib/auth";
import { resolveLanguage } from "../i18n";

async function getHandler(request: NextRequest) {
  try {
    const lang = resolveLanguage({ headers: request.headers });

    const searchParams = Object.fromEntries(
      request.nextUrl.searchParams.entries(),
    );
    const { lang: _, ...filters } = searchParams;

    const events = await getAllEvents(filters, lang);

    if (!events) {
      return NextResponse.json(
        { error: "Falha ao buscar eventos" },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: events });
  } catch (error) {
    console.error("Events API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withA<PERSON><PERSON><PERSON><PERSON>uth(getHandler);
