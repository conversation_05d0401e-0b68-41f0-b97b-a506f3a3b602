import { prisma } from "../app/(site)/libs/prisma";

// import { faker } from "@faker-js/faker";
// import { Category } from "@prisma/client";
// import bcrypt from "bcrypt";
// const prisma = new PrismaClient();

// const categories = [
//   "Shows",
//   "Feiras",
//   "Eventos Políticos",
//   "Workshops",
//   "Eventos Religiosos",
//   "Cursos",
//   "Festivais",
//   "Teatro",
//   "Palestras",
//   "Festas",
//   "Eventos Infantis",
//   "Dança",
//   "Outros",
//   "Exposições",
// ];

// const seedEvents = async () => {
//   await prisma.$executeRaw`TRUNCATE TABLE "categories" CASCADE`;
//   await prisma.$executeRaw`TRUNCATE TABLE "favorite_events" CASCADE`;
//   await prisma.$executeRaw`TRUNCATE TABLE "event_checkouts" CASCADE`;
//   await prisma.$executeRaw`TRUNCATE TABLE "medias" CASCADE`;
//   await prisma.$executeRaw`TRUNCATE TABLE "events" CASCADE`;

//   const categoriesList: Category[] = [];

//   for await (const c of categories) {
//     categoriesList.push(
//       await prisma.category.create({
//         data: {
//           name: c,
//           description: faker.lorem.paragraph(
//             faker.number.int({ max: 2, min: 1 })
//           ),
//           type: "NORMAL",
//           createdBy: {
//             connect: { email: "<EMAIL>" },
//           },
//         },
//       })
//     );
//   }

//   const imageCategories = [
//     "business",
//     "fashion",
//     "city",
//     "nightlife",
//     "sports",
//   ];

//   for (let i = 0; i < 60; i++) {
//     await prisma.event.create({
//       data: {
//         name: faker.lorem.sentence({ min: 2, max: 4 }).substring(0, 100), // Limit title length
//         description: faker.lorem.paragraph(1).substring(0, 500), // Limit description length
//         startAt: faker.date.future({ years: faker.number.int({ min: 2, max: 5 }) }),
//         endAt: faker.date.future({ years: faker.number.int({ min: 2, max: 5 }) }),
//         location: faker.location.streetAddress(),
//         organizer: faker.company.name(),
//         prices: {
//           create: {
//             name: faker.lorem.sentence({ min: 1, max: 3 }),
//             price: faker.number.float({ min: 500, max: 100_000, fractionDigits: 2 }),
//             description: faker.lorem.sentence().substring(0, 200), // Limit price description length
//           },
//         },
//         createdBy: { connect: { email: "<EMAIL>" } },
//         published: true,
//         highlightedUntil: faker.datatype.boolean()
//           ? faker.date.future({ years: faker.number.int({ min: 2, max: 3 }) })
//           : null,
//         medias: {
//           create: {
//             mimetype: "image/jpeg",
//             filename: faker.string.alphanumeric(10),
//             originalName: faker.string.alphanumeric(10),
//             url: faker.image.urlLoremFlickr({
//               width: 1080,
//               height: 1080,
//               category: imageCategories[
//                 faker.number.int({
//                   min: 0,
//                   max: imageCategories.length - 1,
//                 })
//               ],
//             }),
//           },
//         },
//         categories: {
//           connect: {
//             id: categoriesList[
//               faker.number.int({ min: 0, max: categoriesList.length - 1 })
//             ].id,
//           },
//         },
//       },
//     });
//   }
// };

async function main() {
  await prisma.$executeRaw`TRUNCATE TABLE "categories" CASCADE`;
  await prisma.$executeRaw`TRUNCATE TABLE "favorite_events" CASCADE`;
  await prisma.$executeRaw`TRUNCATE TABLE "event_checkouts" CASCADE`;
  await prisma.$executeRaw`TRUNCATE TABLE "medias" CASCADE`;
  await prisma.$executeRaw`TRUNCATE TABLE "events" CASCADE`;
  const user = await prisma.user.findUnique({
    where: { email: "<EMAIL>" },
  });

  if (!user) {
    const admin = await prisma.user.create({
      data: {
        email: "<EMAIL>",
        name: "Denilson Costa Admin",
        emailVerified: new Date(),
        birthDate: new Date(2022, 1, 24),
        role: "ADMIN",
      },
    });

    console.log(admin);
  }

  // await seedEvents();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
