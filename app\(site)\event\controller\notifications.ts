import Expo from "expo-server-sdk";
import { getAllEvents } from ".";
import { eventSchema, EventType } from "../schema";
import { z } from "zod";

const expo = new Expo({});

const schema = z.object({
  description: z.string().optional().nullable(),
  event: eventSchema.pick({
    _id: true,
    name: true,
    medias: true,
    description: true,
  }),
});

export type EventNotificationType = z.infer<typeof schema>;

const createNotification = async ({
  data,
}: {
  data: EventNotificationType;
}) => {
  try {
    const pushToken = "ExponentPushToken[quCpr3OpqsajsJhAVahI1W]";

    if (!Expo.isExpoPushToken(pushToken)) {
      console.error(`Push token ${pushToken} is not a valid Expo push token`);
      return { error: "Invalid push token", status: 400 };
    }

    const message = {
      to: pushToken,
      sound: "default",
      title: data.event.name,
      body:
        data.description ??
        data.event.description.substring(0, 100).concat("..."),
      data: { eventId: data.event._id },
      richContent: {
        image: data.event.medias?.[0]?.urlSmall,
      },
    };

    let chunks = expo.chunkPushNotifications([message]);
    let tickets = [];

    for (let chunk of chunks) {
      try {
        let ticketChunk = await expo.sendPushNotificationsAsync(chunk);
        console.log(ticketChunk);
        tickets.push(...ticketChunk);
      } catch (error) {
        console.error("Error sending notification chunk:", error);
      }
    }

    return { data: "Notification sent successfully", status: 200 };
  } catch (error) {
    console.error("Error in notification route:", error);
    return { error: "Internal Server Error", status: 500 };
  }
};

export const createNotificationController = async ({
  data,
}: {
  data: Partial<EventNotificationType>;
}): Promise<
  { error: string; status: number } | { data: string; status: number }
> => {
  try {
    const notification = schema.parse(data);
    return await createNotification({ data: notification });
  } catch (error) {
    console.error("Error in notification route:", error);
    return { error: "Internal Server Error", status: 500 };
  }
};
export const createEventNotificationController = async ({
  event,
}: {
  event: EventType;
}): Promise<
  { error: string; status: number } | { data: string; status: number }
> => {
  return await createNotification({ data: { event } });
};
