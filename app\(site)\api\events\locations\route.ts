import { NextRequest, NextResponse } from "next/server";
import { getEventsLocations } from "@/app/(site)/event/controller";
import { withApiKeyAuth } from "@/lib/auth";

async function getHandler(request: NextRequest) {
  try {
    const locations = await getEventsLocations();

    if (!locations) {
      return NextResponse.json(
        { error: "Falha ao buscar locais de eventos" },
        { status: 400 },
      );
    }

    return NextResponse.json({ data: locations });
  } catch (error) {
    console.error("Event Locations API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApiKeyAuth(getHandler);
