/**
 * @param {string} s
 * @return {number}
 */
var romanToInt = function (s) {
  const numerals = {
    I: 1,
    IV: 4,
    V: 5,
    IX: 9,
    X: 10,
    XL: 40,
    L: 50,
    XC: 90,
    C: 100,
    CD: 400,
    D: 500,
    CM: 900,
    M: 1000,
  };

  const romanNumerals = s.match(/IV|IX|XL|XC|CD|CM|./g);

  return romanNumerals.reduce((acc, curr) => {
    const num = numerals[curr];
    if (num) acc += num;
    return acc;
  }, 0);
};

console.log(romanToInt("III"));
