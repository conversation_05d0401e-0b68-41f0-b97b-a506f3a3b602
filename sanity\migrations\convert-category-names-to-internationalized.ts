/**
 * Migration script to convert category names from simple strings to internationalized objects
 *
 * This script converts:
 * name: "Category Name"
 *
 * To:
 * name: [{ _key: 'pt', value: "Category Name" }]
 *
 * Usage:
 * - Set SANITY_API_TOKEN environment variable with write permissions
 * - Run: npx tsx sanity/migrations/convert-category-names-to-internationalized.ts
 */

import { createClient } from "next-sanity";
import { apiVersion, dataset, projectId } from "../env";

// Create client with write token
const client = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false, // Don't use CDN for mutations
  token: process.env.SANITY_API_TOKEN,
});

type CategoryDocument = {
  _id: string;
  _type: "category";
  name: string | Array<{ _key: string; value: string }>;
  slug?: {
    _type: "slug";
    current: string;
  };
  description?: string;
  type?: string;
  createdBy?: string;
};

async function migrateCategoryNames() {
  console.log("🚀 Starting category name migration...");

  try {
    // Check if we have a valid token
    if (!process.env.SANITY_API_TOKEN) {
      throw new Error("SANITY_API_TOKEN environment variable is required");
    }

    // Fetch all categories
    console.log("📋 Fetching all categories...");
    const categories = await client.fetch<CategoryDocument[]>(
      `*[_type == "category"]{_id, name, slug, description, type, createdBy}`,
    );

    console.log(`📊 Found ${categories.length} categories to process`);

    if (categories.length === 0) {
      console.log("✅ No categories found. Migration complete.");
      return;
    }

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Process each category
    for (const category of categories) {
      try {
        // Check if name is already internationalized (array format)
        if (Array.isArray(category.name)) {
          console.log(
            `⏭️  Skipping category "${category._id}" - already internationalized`,
          );
          skippedCount++;
          continue;
        }

        // Check if name is a string
        if (typeof category.name !== "string") {
          console.log(
            `⚠️  Skipping category "${category._id}" - name is not a string:`,
            typeof category.name,
          );
          skippedCount++;
          continue;
        }

        // Convert string name to internationalized format
        const internationalizedName = [
          {
            _key: "pt",
            value: category.name,
          },
        ];

        // Update the document
        console.log(
          `🔄 Converting category "${category._id}": "${category.name}"`,
        );

        await client
          .patch(category._id)
          .set({ name: internationalizedName })
          .commit();

        migratedCount++;
        console.log(`✅ Successfully migrated category "${category._id}"`);
      } catch (error) {
        errorCount++;
        console.error(`❌ Error migrating category "${category._id}":`, error);
      }
    }

    // Summary
    console.log("\n📈 Migration Summary:");
    console.log(`✅ Successfully migrated: ${migratedCount} categories`);
    console.log(`⏭️  Skipped (already migrated): ${skippedCount} categories`);
    console.log(`❌ Errors: ${errorCount} categories`);
    console.log(`📊 Total processed: ${categories.length} categories`);

    if (errorCount > 0) {
      console.log(
        "\n⚠️  Some categories failed to migrate. Please check the errors above.",
      );
      process.exit(1);
    } else {
      console.log("\n🎉 Migration completed successfully!");
    }
  } catch (error) {
    console.error("💥 Migration failed:", error);
    process.exit(1);
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  migrateCategoryNames()
    .then(() => {
      console.log("🏁 Migration script finished");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Migration script failed:", error);
      process.exit(1);
    });
}

export { migrateCategoryNames };
