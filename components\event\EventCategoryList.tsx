import React, { Fragment, useEffect } from "react";
import { ActivityIndicator, ScrollView } from "react-native";
import useGetCategories from "src/categories/hooks/useGetCategories";
import { RenderIf } from "src/core/components/RenderIf";
import Gap from "../../core/components/Gap";
import globalStyles from "../../styles/global";
import EventCategoryCard from "./EventCategoryCard/EventCategoryCard";

/**
 * @deprecated Planed to be removed in the future
 */
export default function EventCategoryList() {
  const { categories, isLoading, isFetching } = useGetCategories({
    type: "CUSTOM",
  });

  return (
    <>
      <RenderIf isTrue={!isLoading}>
        <ScrollView
          horizontal
          decelerationRate='normal'
          snapToInterval={300}
          snapToAlignment={"center"}
          style={{ paddingBottom: globalStyles.gap["2xs"] }}
        >
          {categories.map((item) => (
            <Fragment key={item.id}>
              <EventCategoryCard category={item} />
              <Gap x={globalStyles.gap.xs} />
            </Fragment>
          ))}
        </ScrollView>
      </RenderIf>
      <RenderIf isTrue={isLoading || isFetching}>
        <ActivityIndicator size='large' color={globalStyles.colors.primary2} />
      </RenderIf>
    </>
  );
}
