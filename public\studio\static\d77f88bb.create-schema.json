[{"options": {"apiVersion": "2022-11-27", "languages": [{"id": "pt", "title": "Português"}, {"id": "en", "title": "English"}]}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "of": [{"type": "internationalizedArrayStringValue", "title": "Internationalized array string"}], "name": "internationalizedArrayString", "type": "array", "title": "Internationalized array"}, {"options": {"apiVersion": "2022-11-27", "languages": [{"id": "pt", "title": "Português"}, {"id": "en", "title": "English"}]}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "of": [{"type": "internationalizedArrayTextValue", "title": "Internationalized array text"}], "name": "internationalizedArrayText", "type": "array", "title": "Internationalized array"}, {"fields": [{"name": "value", "type": "string"}], "name": "internationalizedArrayStringValue", "type": "object", "title": "Internationalized array string"}, {"fields": [{"name": "value", "type": "text"}], "name": "internationalizedArrayTextValue", "type": "object", "title": "Internationalized array text"}, {"of": [{"marks": {"annotations": [{"fields": [{"validation": [{"rules": [{"flag": "uri", "constraint": {"options": {"scheme": ["/^http$/", "/^https$/"], "allowRelative": false, "relativeOnly": false, "allowCredentials": false}}}], "level": "error"}], "name": "href", "type": "url", "title": "URL"}], "name": "link", "type": "object", "title": "URL"}], "decorators": [{"value": "strong", "title": "Strong"}, {"value": "em", "title": "Emphasis"}]}, "lists": [{"value": "bullet", "title": "Bullet"}], "styles": [{"value": "normal", "title": "Normal"}, {"value": "h1", "title": "H1"}, {"value": "h2", "title": "H2"}, {"value": "h3", "title": "H3"}, {"value": "h4", "title": "H4"}, {"value": "blockquote", "title": "Quote"}], "of": [], "type": "block"}, {"options": {"hotspot": true}, "fields": [{"name": "alt", "type": "string", "title": "Alternative Text"}], "type": "image"}], "name": "blockContent", "type": "array"}, {"fields": [{"options": {"apiVersion": "2022-11-27", "languages": [{"id": "pt", "title": "Português"}, {"id": "en", "title": "English"}]}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "name": "name", "type": "internationalizedArrayString", "title": "Nome"}, {"options": {"source": "name"}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "name": "slug", "type": "slug"}, {"validation": [{"rules": [{"flag": "max", "constraint": 500}, {"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "description", "type": "text", "title": "Descrição"}, {"options": {"list": [{"title": "Normal", "value": "NORMAL"}, {"title": "Personalizado", "value": "CUSTOM"}]}, "initialValue": "NORMAL", "name": "type", "type": "string", "title": "Tipo"}, {"validation": [{"rules": [{"flag": "max", "constraint": 50}, {"flag": "presence", "constraint": "required"}], "level": "error"}], "name": "created<PERSON>y", "type": "string", "title": "<PERSON><PERSON><PERSON> por"}], "name": "category", "type": "document", "title": "Categorias"}, {"fields": [{"options": {"apiVersion": "2022-11-27", "languages": [{"id": "pt", "title": "Português"}, {"id": "en", "title": "English"}]}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "name": "name", "type": "internationalizedArrayString", "title": "Nome"}, {"options": {"source": "name"}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "name": "slug", "type": "slug"}, {"validation": [{"rules": [{"flag": "max", "constraint": 50}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "Organizador é obrigatório"}], "name": "organizer", "type": "string", "title": "Organizador"}, {"options": {"apiVersion": "2022-11-27", "languages": [{"id": "pt", "title": "Português"}, {"id": "en", "title": "English"}]}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "name": "description", "type": "internationalizedArrayText", "title": "Descrição"}, {"validation": [{"rules": [{"flag": "max", "constraint": 100}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "Local é obrigatório"}], "name": "location", "type": "string", "title": "Local"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Data de início é obrigatória"}], "name": "startAt", "type": "datetime", "title": "Data de Início"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Data de fim é obrigatória"}], "name": "endAt", "type": "datetime", "title": "Data de Fim"}, {"name": "highlightedUntil", "type": "datetime", "title": "Em destaque até"}, {"name": "sponsoredUntil", "type": "datetime", "title": "Patrocinado até"}, {"validation": [{"rules": [{"flag": "min", "constraint": 1}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "Ao menos um preço deve ser definido"}], "of": [{"fields": [{"validation": [{"rules": [{"flag": "max", "constraint": 50}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "Nome é obrigatório"}], "name": "name", "type": "string"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Preço é obrigatório"}], "name": "price", "type": "number"}, {"validation": [{"rules": [{"flag": "max", "constraint": 1000}], "level": "warning", "message": "Descrição deve ter no máximo 1000 caracteres"}], "name": "description", "type": "text"}], "type": "object"}], "name": "prices", "type": "array", "title": "Preços"}, {"of": [{"to": [{"type": "category"}], "type": "reference", "title": "Reference to categorias"}], "name": "categories", "type": "array", "title": "Categorias"}, {"of": [{"options": {"hotspot": true}, "fields": [{"validation": [{"rules": [{"flag": "max", "constraint": 200}], "level": "warning", "message": "Descrição deve ter no máximo 200 caracteres"}], "name": "alt", "type": "string"}], "type": "image"}], "name": "medias", "type": "array", "title": "Imagens"}, {"validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "of": [{"fields": [{"options": {"apiVersion": "2022-11-27", "languages": [{"id": "pt", "title": "Português"}, {"id": "en", "title": "English"}]}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "name": "description", "type": "internationalizedArrayText", "title": "Descrição"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Hora de início é obrigatória"}], "name": "startTime", "type": "datetime", "title": "Hora de Início"}, {"description": "Opcional - deixe em branco se a atividade não tem hora de fim definida", "name": "endTime", "type": "datetime", "title": "<PERSON><PERSON>"}, {"options": {"apiVersion": "2022-11-27", "languages": [{"id": "pt", "title": "Português"}, {"id": "en", "title": "English"}]}, "validation": [{"rules": [{"flag": "custom"}], "level": "error"}], "name": "location", "type": "internationalizedArrayString", "title": "Local"}], "type": "object", "title": "Atividade"}], "name": "activities", "type": "array", "title": "Atividades"}, {"of": [{"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Nome é obrigatório"}], "name": "name", "type": "string", "title": "Nome"}, {"options": {"list": [{"title": "WhatsApp", "value": "WHATSAPP"}, {"title": "Website", "value": "WEBSITE"}, {"title": "Email", "value": "EMAIL"}, {"title": "Telefone", "value": "PHONE"}], "layout": "dropdown"}, "validation": [{"rules": [{"flag": "valid", "constraint": ["WHATSAPP", "WEBSITE", "EMAIL", "PHONE"]}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "Tipo é obrigatório"}], "name": "type", "type": "string", "title": "Tipo"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Valor é obrigatório"}], "name": "value", "type": "string", "title": "Valor"}], "type": "object"}], "name": "checkoutMethods", "type": "array", "title": "Métodos de Checkout"}, {"validation": [{"rules": [{"flag": "max", "constraint": 50}, {"flag": "presence", "constraint": "required"}], "level": "error", "message": "Nome do criador é obrigatório"}], "name": "created<PERSON>y", "type": "string", "title": "<PERSON><PERSON><PERSON> por"}], "name": "event", "type": "document", "title": "Eventos"}, {"fields": [{"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "ID do Usuário é obrigatório"}], "name": "userId", "type": "string", "title": "ID do Usuário"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "ID do Evento é obrigatório"}], "name": "eventId", "type": "string", "title": "ID do Evento"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "Referência do Evento é obrigatória"}], "to": [{"type": "event"}], "name": "event", "type": "reference", "title": "Evento"}], "name": "favoriteEvent", "type": "document", "title": "Eventos favoritos"}, {"fields": [{"validation": [{"rules": [{"flag": "max", "constraint": 150}], "level": "warning", "message": "A descrição deve ter no máximo 150 caracteres"}], "name": "description", "type": "text", "title": "Descrição"}, {"validation": [{"rules": [{"flag": "presence", "constraint": "required"}], "level": "error", "message": "O evento é obrigatório"}], "to": [{"type": "event"}], "name": "event", "type": "reference", "title": "Evento"}], "name": "notification", "type": "document", "title": "Notificações"}]