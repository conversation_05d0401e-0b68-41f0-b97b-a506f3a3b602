import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { getCurrentLanguage, resources } from ".";

const initI18n = async () => {
  const initialLanguage = await getCurrentLanguage();

  await i18n.use(initReactI18next).init({
    resources,
    lng: initialLanguage,
    fallbackLng: "pt",
    compatibilityJSON: "v4",
    interpolation: {
      escapeValue: false,
    },
    debug: __DEV__,
  });
};

initI18n();

export default i18n;
